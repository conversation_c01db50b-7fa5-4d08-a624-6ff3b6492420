import React, { useState, useEffect, useMemo } from "react";
import { useDispatch } from "react-redux";
import {
  Button,
  Divider,
  Form,
  Grid,
  Header,
  Message,
  Segment,
  TextArea,
  Popup,
  Icon
} from "semantic-ui-react";
import { useMediaQuery } from "react-responsive";
import { toast } from "react-toastify";
import { Formik, Form as FormikForm } from "formik";
import * as Yup from "yup";
import ModalWrapper from "../../../app/common/modals/modalWrapper";
import { closeModal } from "../../../app/common/modals/modalSlice";
import MyCheckbox from "../../../app/common/form/MyCheckbox";
import {
  partyIsBuyerAgent,
  partyIsListingAgent,
} from "../../../app/common/util/util";
import {
  sendDocSharingEmail,
  addHistoryToDb,
  updateDocSentForSigningInDb,
} from "../../../app/firestore/firestoreService";

export default function AgentSendForSigning({ doc, party, transaction, allParties }) {
  const dispatch = useDispatch();
  const isMobile = useMediaQuery({ query: "(max-width:768px)" });
  const [selectedClients, setSelectedClients] = useState([]);
  const [note, setNote] = useState("");
  const [processing, setProcessing] = useState(false);

  // Get other-side client parties from transaction parties only
  const otherSideClientParties = useMemo(() => {
    if (!allParties || !transaction.agentRepresents) {
      return [];
    }

    let otherSideRoles = [];
    if (transaction.agentRepresents === "Buyer") {
      otherSideRoles = ["Seller", "Seller 2", "Seller 3"];
    } else if (transaction.agentRepresents === "Seller") {
      otherSideRoles = ["Buyer", "Buyer 2", "Buyer 3"];
    }

    return allParties.filter(p => otherSideRoles.includes(p.role));
  }, [allParties, transaction.agentRepresents]);

  // Filter annotsInProgressSuggested for relevant signerRoles and signature/date types - memoized
  const relevantAnnots = useMemo(() => {
    return doc.annotsInProgressSuggested?.filter(annot => {
      // Include signature and date type annotations
      if (annot.type !== "signature" && annot.type !== "date") {
        return false;
      }

      if (partyIsBuyerAgent(party.role) && annot.signerRole?.startsWith("Buyer")) {
        return true;
      }
      if (partyIsListingAgent(party.role) && annot.signerRole?.startsWith("Seller")) {
        return true;
      }
      return false;
    }) || [];
  }, [doc.annotsInProgressSuggested, party.role]);

  // Get only signature annotations for client selection (dates are auto-signed with signatures)
  const signatureAnnots = useMemo(() => {
    return relevantAnnots.filter(annot => annot.type === "signature");
  }, [relevantAnnots]);

  // Create initial client list with auto-populated names (only for clients that exist in transaction parties)
  useEffect(() => {
    // Only include signature annotations that have corresponding parties in the transaction
    const filteredSignatureAnnots = signatureAnnots.filter(annot =>
      otherSideClientParties.some(p => p.role === annot.signerRole)
    );

    const initialClients = filteredSignatureAnnots.map(annot => {
      const existingParty = otherSideClientParties.find(p => p.role === annot.signerRole);
      const fullName = existingParty?.firstName && existingParty?.lastName
        ? `${existingParty.firstName} ${existingParty.lastName}`
        : "";
      return {
        role: annot.signerRole,
        fullName: fullName,
        email: existingParty?.email || "",
        isFromTransaction: true, // All parties from transaction parties are considered "from transaction" (names uneditable)
        selected: true,
      };
    });
    setSelectedClients(initialClients);
  }, [signatureAnnots, otherSideClientParties]);



  function handleToggleClient(index) {
    const updatedClients = [...selectedClients];
    updatedClients[index].selected = !updatedClients[index].selected;
    setSelectedClients(updatedClients);
  }

  function handleClientChange(index, field, value) {
    const updatedClients = [...selectedClients];
    updatedClients[index][field] = value;
    setSelectedClients(updatedClients);
  }

  async function handleSendForSigning() {
    setProcessing(true);
    try {
      const clientsToSend = selectedClients.filter(client => client.selected);
      
      if (clientsToSend.length === 0) {
        toast.error("Please select at least one client to send for signing.");
        setProcessing(false);
        return;
      }

      // Validate all selected clients have required fields
      const invalidClients = clientsToSend.filter(
        client => !client.fullName || !client.email
      );

      if (invalidClients.length > 0) {
        toast.error("Please fill in all required fields for selected clients.");
        setProcessing(false);
        return;
      }

      const uniqueEmails = clientsToSend.filter(
        client => client.email && clientsToSend.filter(c => c.email === client.email).length === 1
      )

      if (uniqueEmails.length !== clientsToSend.length) {
        toast.error("Please enter unique email addresses for selected clients.");
        setProcessing(false);
        return;
      }

      // Update document status and mark as sent by agent
      const docUpdateFields = {
        sentForSigningByAgent: true,
        sentByAgentId: party.id,
        sentByAgentAt: new Date(),
      };

      // Add agent marker to signingRequestedFor and split fullName
      const updatedClientsToSend = clientsToSend.map(client => {
        const nameParts = client.fullName.trim().split(' ');
        const firstName = nameParts[0] || '';
        const lastName = nameParts.slice(1).join(' ') || '';

        return {
          ...client,
          firstName,
          lastName,
          sentByAgent: true,
          agentId: party.id,
        };
      });

      // First, we need to move the relevant annotations from annotsInProgressSuggested to annotsInProgress
      // so that updateDocSentForSigningInDb can process them correctly
      const relevantAnnotsForSigning = relevantAnnots.filter(annot =>
        updatedClientsToSend.some(client => client.role === annot.signerRole)
      );

      // Create a copy of the document with the annotations moved to annotsInProgress
      const docWithAnnotsInProgress = {
        ...doc,
        annotsInProgress: relevantAnnotsForSigning
      };
      console.log("docWithAnnotsInProgress: ", docWithAnnotsInProgress);
      console.log("updatedClientsToSend: ", updatedClientsToSend);
      await updateDocSentForSigningInDb(docWithAnnotsInProgress, note, updatedClientsToSend, transaction, docUpdateFields);

      // Send emails to clients
      await sendDocSharingEmail(
        updatedClientsToSend,
        "signing",
        party, // The agent is the sender
        transaction
      );

      // Add history entries
      updatedClientsToSend.forEach((client) => {
        addHistoryToDb(
          transaction.id,
          party,
          "sent for signing by agent",
          doc.name,
          client
        );
      });

      setProcessing(false);
      toast.success("Document successfully sent for signing to selected clients");
      dispatch(closeModal({ modalType: "AgentSendForSigning" }));
    } catch (error) {
      toast.error(error.message);
      setProcessing(false);
    }
  }

  function handleCancel() {
    dispatch(closeModal({ modalType: "AgentSendForSigning" }));
  }

  return (
    <ModalWrapper size="small">
      <Segment>
        <Grid>
          <Grid.Column width={16} className="zero bottom padding">
            <Header as="h2" color="blue" className="zero bottom margin">
              Send for Signing
            </Header>
            <Divider className="zero bottom margin" />
            <p>
              Select your clients to send this document for signing.
              <br/>Enter the name they should sign with, and email address.
              {!isMobile && (
                <Popup
                  flowing
                  size="small"
                  trigger={
                    <Icon
                      name="info"
                      color="blue"
                      circular
                      inverted
                      size="small"
                      style={{ marginLeft: "3px", marginBottom: "3px" }}
                    />
                  }
                >
                  <p className="bold text blue mini bottom margin">
                    How Your Clients Can Sign
                  </p>
                  <p className="bold text blue mini bottom margin"></p>
                  <p className="text-dark text-normal mini bottom margin">
                    {" "}
                    Your clients will receive an email from transaction.com
                    <br />
                    with a blue button link labeled "Sign Documents".
                    <br />
                    When they click on the button, they will be
                    <br />
                    prompted to log in with their email
                    <br />
                    (same process you went through).
                    <br />
                    If they have not logged in before,
                    <br />
                    it will prompt them to create a password.
                    <br />
                    Once logged in, they will see a similar Client Portal <br />
                    that you see, with the documents they need to sign
                    <br />
                    at the top in bright orange and a button labeled "Sign".
                    <br/><i>Questions? Contact support: <EMAIL></i>
                  </p>
                </Popup>
              )}
            </p>
          </Grid.Column>

          <Grid.Column width={16}>
            {selectedClients.length === 0 ? (
              <Message warning>
                <Message.Header>No Signing Fields Found</Message.Header>
                <p>
                  This document doesn't have any signing fields for your
                  clients.
                </p>
              </Message>
            ) : (
              <Formik
                initialValues={{}}
                validationSchema={Yup.object({})}
                onSubmit={() => {}}
              >
                {() => (
                  <FormikForm>
                    {selectedClients.map((client, index) => (
                      <Segment key={index} style={{ marginBottom: "10px" }}>
                        {isMobile ? (
                          // Mobile layout: checkbox and role on one line, name and email on separate lines
                          <Grid>
                            <Grid.Row>
                              <Grid.Column width={2}>
                                <MyCheckbox
                                  name={`selected_${index}`}
                                  checked={client.selected}
                                  onChange={() => handleToggleClient(index)}
                                />
                              </Grid.Column>
                              <Grid.Column width={14}>
                                <Header as="h4" style={{ margin: 0 }}>
                                  {client.role}
                                </Header>
                              </Grid.Column>
                            </Grid.Row>
                            <Grid.Row>
                              <Grid.Column width={16}>
                                <input
                                  type="text"
                                  placeholder="Full Name"
                                  value={client.fullName}
                                  onChange={(e) =>
                                    handleClientChange(
                                      index,
                                      "fullName",
                                      e.target.value
                                    )
                                  }
                                  disabled={client.isFromTransaction}
                                  style={{
                                    width: "100%",
                                    padding: "8px",
                                    border: "1px solid rgba(34,36,38,.15)",
                                    borderRadius: "4px",
                                    fontSize: "14px",
                                    backgroundColor: client.isFromTransaction ? "#f9f9f9" : "white",
                                    cursor: client.isFromTransaction ? "not-allowed" : "text"
                                  }}
                                />
                              </Grid.Column>
                            </Grid.Row>
                            <Grid.Row>
                              <Grid.Column width={16}>
                                <input
                                  type="email"
                                  placeholder="Email"
                                  value={client.email}
                                  onChange={(e) =>
                                    handleClientChange(
                                      index,
                                      "email",
                                      e.target.value
                                    )
                                  }
                                  style={{
                                    width: "100%",
                                    padding: "8px",
                                    border: "1px solid rgba(34,36,38,.15)",
                                    borderRadius: "4px",
                                    fontSize: "14px"
                                  }}
                                />
                              </Grid.Column>
                            </Grid.Row>
                          </Grid>
                        ) : (
                          // Desktop layout: checkbox, role, name, and email in one line
                          <Grid>
                            <Grid.Row>
                              <Grid.Column width={1}>
                                <MyCheckbox
                                  name={`selected_${index}`}
                                  checked={client.selected}
                                  onChange={() => handleToggleClient(index)}
                                />
                              </Grid.Column>
                              <Grid.Column width={3}>
                                <Header as="h4" style={{ margin: 0 }}>
                                  {client.role}
                                </Header>
                              </Grid.Column>
                              <Grid.Column width={5}>
                                <input
                                  type="text"
                                  placeholder="Full Name"
                                  value={client.fullName}
                                  onChange={(e) =>
                                    handleClientChange(
                                      index,
                                      "fullName",
                                      e.target.value
                                    )
                                  }
                                  disabled={client.isFromTransaction}
                                  style={{
                                    height: "28px",
                                    minWidth: "140px",
                                    width: "100%",
                                    padding: "6px 8px",
                                    border: "1px solid rgba(34,36,38,.15)",
                                    borderRadius: "4px",
                                    fontSize: "14px",
                                    backgroundColor: client.isFromTransaction ? "#f9f9f9" : "white",
                                    cursor: client.isFromTransaction ? "not-allowed" : "text"
                                  }}
                                />
                              </Grid.Column>
                              <Grid.Column width={7}>
                                <input
                                  type="email"
                                  placeholder="Email"
                                  value={client.email}
                                  onChange={(e) =>
                                    handleClientChange(
                                      index,
                                      "email",
                                      e.target.value
                                    )
                                  }
                                  style={{
                                    height: "28px",
                                    minWidth: "220px",
                                    width: "100%",
                                    padding: "6px 8px",
                                    border: "1px solid rgba(34,36,38,.15)",
                                    borderRadius: "4px",
                                    fontSize: "14px"
                                  }}
                                />
                              </Grid.Column>
                            </Grid.Row>
                          </Grid>
                        )}
                      </Segment>
                    ))}
                  </FormikForm>
                )}
              </Formik>
            )}
          </Grid.Column>

          <Grid.Column width={16}>
            <Header as="h3" color="blue">
              Add a note (optional)
            </Header>
            <Form>
              <TextArea
                rows={4}
                value={note}
                onChange={(e) => setNote(e.target.value)}
                placeholder="Add a message for your clients..."
              />
            </Form>
          </Grid.Column>

          <Grid.Column width={16} className="zero top padding">
            <Divider className="zero top margin" />
            {processing ? (
              <Button
                primary
                loading
                floated={isMobile ? null : "right"}
                className={isMobile ? "fluid medium bottom margin" : null}
              >
                Sending...
              </Button>
            ) : (
              <Button
                floated={isMobile ? null : "right"}
                primary
                onClick={handleSendForSigning}
                // disabled={
                //   selectedClients.filter((c) => c.selected).length === 0
                // }
                className={isMobile ? "fluid medium bottom margin" : null}
              >
                Send for Signatures
              </Button>
            )}

            <Button
              floated={isMobile ? null : "right"}
              onClick={handleCancel}
              className={isMobile ? "fluid medium bottom margin" : null}
            >
              Cancel
            </Button>
          </Grid.Column>
        </Grid>
      </Segment>
    </ModalWrapper>
  );
}
