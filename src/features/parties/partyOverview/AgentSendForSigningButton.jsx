import React, { useMemo } from "react";
import { <PERSON><PERSON>, Popup } from "semantic-ui-react";
import { openModal } from "../../../app/common/modals/modalSlice";
import { useDispatch } from "react-redux";
import { hasOtherSideClients } from "../../../app/common/util/util";

export default function AgentSendForSigningButton({ doc, party, transaction, allParties, isMobile }) {
  const dispatch = useDispatch();



  // Determine button state based on document status and signing details
  const buttonState = useMemo(() => {
    // Check if there are other-side clients in the transaction
    const hasOtherSideClientsInTransaction = hasOtherSideClients(allParties, transaction.agentRepresents);

    // Check if document was sent by this agent using the agent-specific fields
    const wasSentByThisAgent = doc.sentForSigningByAgent && doc.sentByAgentId === party.id;

    if (!wasSentByThisAgent) {
      return {
        text: "Send for Signing",
        color: "blue",
        disabled: !hasOtherSideClientsInTransaction,
        status: "ready"
      };
    }

    // Document was sent by this agent - check signing status
    const signingDetails = doc.signingDetails || {};

    // Only check roles that were actually selected for signing (exist in signingDetails)
    const selectedClientRoles = Object.keys(signingDetails).filter(roleKey => {
      const signingDetail = signingDetails[roleKey];
      // Check if this signing detail was sent by this agent
      return signingDetail && signingDetail.sentByAgent && signingDetail.agentId === party.id;
    });

    let allSigned = true;
    let anySigned = false;

    selectedClientRoles.forEach(roleKey => {
      const signingDetail = signingDetails[roleKey];

      if (signingDetail.signed) {
        anySigned = true;
      } else {
        allSigned = false;
      }
    });

    if (allSigned && selectedClientRoles.length > 0) {
      return {
        text: "Clients Signed",
        color: "green",
        disabled: false,
        status: "complete"
      };
    } else if (anySigned || selectedClientRoles.length > 0) {
      return {
        text: "Waiting for Signatures",
        color: "orange",
        disabled: false,
        status: "waiting"
      };
    } else {
      return {
        text: "Send for Signing",
        color: "blue",
        disabled: false,
        status: "ready"
      };
    }
  }, [doc.sentForSigningByAgent, doc.sentByAgentId, doc.signingDetails, party.id, allParties, transaction.agentRepresents]);

  // Create popup content with client status
  const popupContent = useMemo(() => {
    if (buttonState.status === "ready") {
      return (
        <div>
          <p><strong>Send for Signing</strong></p>
          <p>Click to send this document to your clients for electronic signature. 
            <br />You can select which clients to send to and add a personal note.
            <br />This document and your clients' signatures are viewable by the other agent,
            <br/>so no need to send it back to the other agent once your clients sign via TransActioner.</p>
        </div>
      );
    }

    const signingDetails = doc.signingDetails || {};

    // Only show status for clients that were actually selected for signing by this agent
    const selectedClientRoles = Object.keys(signingDetails).filter(roleKey => {
      const signingDetail = signingDetails[roleKey];
      return signingDetail && signingDetail.sentByAgent && signingDetail.agentId === party.id;
    });

    const statusList = selectedClientRoles.map(roleKey => {
      const signingDetail = signingDetails[roleKey];
      const status = signingDetail?.signed ? "signed" : "waiting for signature";
      const clientName = signingDetail?.firstName && signingDetail?.lastName
        ? `${signingDetail.firstName} ${signingDetail.lastName}`
        : signingDetail?.role || roleKey;
      const clientEmail = signingDetail?.email?.length > 0 ? `(${signingDetail.email})` : "";
      const role = signingDetail?.role || roleKey;

      return `${role} ${clientName !== role ? `(${clientName})` : ""}: ${status} ${clientEmail}`;
    });

    return (
      <div>
        
        <p className="bold text blue mini bottom margin">
          Signing Status
        </p>
        {statusList.map((status, index) => (
          <p key={index} style={{ margin: "2px 0" }}>
            • {status}
          </p>
        ))}
        {buttonState.status === "waiting" && (
          <p style={{ marginTop: "8px", fontStyle: "italic" }}>
            Clients will receive email notifications to sign the document.
            <br />
            You will receive an email notification once each client signs.
            <br />
            If you don't see an email, check your Spam folder.
          </p>
        )}
        {buttonState.status === "complete" && (
          <p style={{ marginTop: "8px", fontStyle: "italic" }}>
            Your clients have signed.
            <br />
            The TransActioner agent has a copy of this signed document,
            <br />
            and received an email notification that your clients have signed.
          </p>
        )}
      </div>
    );
  }, [buttonState.status, doc.signingDetails, party.id]);

  function handleSendForSigning() {
    if (buttonState.status === "complete") {
      return;
    }
    dispatch(
      openModal({
        modalType: "AgentSendForSigning",
        modalProps: {
          doc,
          party,
          transaction,
          allParties,
        },
      })
    );
  }

  // Check if button should be hidden due to no other-side clients
  const hasOtherSideClientsInTransaction = hasOtherSideClients(allParties, transaction.agentRepresents);
  const shouldHideButton = !hasOtherSideClientsInTransaction && !doc.sentForSigningByAgent;

  // Don't render the button at all if there are no other-side clients and document wasn't sent by agent
  if (shouldHideButton) {
    return null;
  }

  return (
    <Popup
      content={popupContent}
      trigger={
        <Button
          color={buttonState.color}
          floated={isMobile ? null : "right"}
          className={isMobile ? "fluid medium bottom margin" : null}
          size="small"
          disabled={buttonState.disabled}
          onClick={() => handleSendForSigning()}
        >
          {buttonState.text}
        </Button>
      }
      position="top center"
      wide="very"
    />
  );
}
