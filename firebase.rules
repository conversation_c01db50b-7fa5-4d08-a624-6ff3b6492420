rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
  
  function isAgentInSameTransaction() {
    // Check if the current user is an agent party in the same transaction
    // Allow agents to update other parties in the same transaction
    // This is a simplified approach that allows any authenticated user to update parties
    // in the same transaction (the business logic ensures only agents call this)
    return request.auth.token.email != null && isSignedIn() &&
    (request.auth.token.email == get(/databases/$(database)/documents/transactions/$(request.resource.data.transactionId)).data.sharingWithRole.listingAgent
    || request.auth.token.email == get(/databases/$(database)/documents/transactions/$(request.resource.data.transactionId)).data.sharingWithRole.buyerAgent)
        &&   exists(/databases/$(database)/documents/transactions/$(resource.data.transactionId));
  }

  
    function allowsTcAccess() {
      return request.auth.uid == get(/databases/$(database)/documents/transactions/$(resource.data.transactionId)).data.tcId;
    }
    
    function willAllowTcAccess() {
      return request.auth.uid == get(/databases/$(database)/documents/transactions/$(request.resource.data.transactionId)).data.tcId;
    }
    
    function willAllowManagerAccess() {
      return request.auth.uid == get(/databases/$(database)/documents/transactions/$(request.resource.data.transactionId)).data.managerId 
	|| (request.auth.r == "g" && request.auth.m[0] == get(/databases/$(database)/documents/transactions/$(request.resource.data.transactionId)).data.managerId);
    }
    
    function willAllowCoAgentAccess() {
      return request.auth.uid == get(/databases/$(database)/documents/transactions/$(request.resource.data.transactionId)).data.coAgentId;
    }
    
    /* Agent as TC */
// The main function for your rule, as defined in a 'match' block
function allowsAgentAsTcAccess() {
  // First, ensure the transactionId exists and is a valid string.
  // This prevents 'get()' from being called with a malformed path.
  return resource.data.transactionId is string &&
         resource.data.transactionId.size() > 0 &&
         // Now, pass *both* the transactionId AND the 'database' variable
         // to the helper function. The 'database' variable here is provided
         // by the top-level 'match /databases/{database}/documents' context.
         _checkTransactionAgentAsTcAccess(database, resource.data.transactionId);
}

// This is the new helper function, now expecting 'dbId' (or 'database') as its first argument.
function _checkTransactionAgentAsTcAccess(dbId, transactionId) {
  // Use the passed-in 'dbId' to correctly construct the path.
  let transactionDoc = get(/databases/$(dbId)/documents/transactions/$(transactionId));

  // Now, perform the document existence and data checks.
  return transactionDoc.exists &&
         transactionDoc.data.agentAsTcId is string && // Good practice to check type!
         request.auth.uid == transactionDoc.data.agentAsTcId;
}

function willAllowAgentAsTcAccess() {
      return request.auth.uid == get(/databases/$(database)/documents/transactions/$(request.resource.data.transactionId)).data.agentAsTcId;
}  
function isAgentAsTc() {
  return request.auth.uid == resource.data.agentAsTcId;
}
function isOtherAgentAsTc() {
  return request.auth.token.email == resource.data.agentAsTransactionCoordinator.email;
}
function willBeAgentAsTc() {
  return request.auth.uid == request.resource.data.agentAsTcId;
}
/* end agent as TC */
    
		match /formTemplates/{document=**} {
		  allow read, update, delete: if isSignedIn() && isAuthor();
      allow create: if isSignedIn() && willBeAuthor();
		}
    match /deadlineTemplates/{document=**} {
		  allow read, update, delete: if isSignedIn() && isAuthor();
      allow create: if isSignedIn() && willBeAuthor();
		}
    match /taskTemplates/{document=**} {
    	allow read: if isSignedIn() && (isAuthor() || isTemplateAuthorMyManager());
		  allow update, delete: if isSignedIn() && isAuthor();
      allow create: if isSignedIn() && willBeAuthor();
		}
    match /ceCerts/{document=**} {
		  allow read, update, delete: if isSignedIn() && isAuthor();
      allow create: if isSignedIn() && willBeAuthor();
		}
    match /transactions/{transactionId} {
//		  allow read, update, delete: if isSignedIn() && (isAuthor() || isAssistant() || isManagerAssistant() || isTc() || isOtherTc() || isCoAgent() || isOtherCoAgent() || isManager() || isSharingWithRole() || isSharingWith());
//		  allow create: if isSignedIn() && (willBeAuthor() || willBeAssistant() || willBeManagerAssistant() || willBeTc() || willBeCoAgent() || willBeManager());
		  allow read, update, delete: if isSignedIn() && (isAuthor() || isAssistant() || isManagerAssistant() || isTc() || isAgentAsTc() || isOtherTc() || isOtherAgentAsTc() || isCoAgent() || isOtherCoAgent() || isManager() || isSharingWithRole() || isSharingWith());
		  allow create: if isSignedIn() && (willBeAuthor() || willBeAssistant() || willBeManagerAssistant() || willBeTc() || willBeAgentAsTc() || willBeCoAgent() || willBeManager());

}
    match /transactionsPublic/{document=**} {
      allow read;
      allow write: if isSignedIn();
    }
    match /tasks/{taskId} {
//		  allow read, update, delete: if isSignedIn() && (isAuthor() || isAssistant() || isManagerAssistant() || isTc() || isOtherTc() || isCoAgent() || isOtherCoAgent() || isManager() || allowsTcAccess() || isSharingWith() || isSharingWithRole());
//		  allow create: if isSignedIn() && (willBeAuthor() || willBeAssistant() || willBeManagerAssistant() || willBeTc() || willBeCoAgent() || willBeManager());
		  allow read, update, delete: if isSignedIn() && (isAuthor() || isAssistant() || isManagerAssistant() || isTc() || isAgentAsTc() || isOtherTc() || isOtherAgentAsTc() || isCoAgent() || isOtherCoAgent() || isManager() || allowsTcAccess() || allowsAgentAsTcAccess() || isSharingWith() || isSharingWithRole());
		  allow create: if isSignedIn() && (willBeAuthor() || willBeAssistant() || willBeManagerAssistant() || willBeTc() || willBeAgentAsTc() || willBeCoAgent() || willBeManager());

}
		match /documents/{documentId} {
//		  allow read, update, delete: if isSignedIn() && (isAuthor() || isAssistant() || isManagerAssistant() || isTc() || isOtherTc() || isCoAgent() || isOtherCoAgent() || isManager() || allowsTcAccess() || isSharingWith() || isSharingWithRole());
//      allow create: if isSignedIn() && (willBeAuthor() || willBeAssistant() || willBeManagerAssistant() || willBeTc() || willBeCoAgent() || willBeManager());
		  allow read, update, delete: if isSignedIn() && (isAuthor() || isAssistant() || isManagerAssistant() || isTc() || isAgentAsTc() || isOtherTc() || isOtherAgentAsTc() || isCoAgent() || isOtherCoAgent() || isManager() || allowsTcAccess() || allowsAgentAsTcAccess() || isSharingWith() || isSharingWithRole());
      allow create: if isSignedIn() && (willBeAuthor() || willBeAssistant() || willBeManagerAssistant() || willBeTc() || willBeAgentAsTc() || willBeCoAgent() || willBeManager());

}
		match /documents/{documentId}/annots/{annotId} {
//		  allow read, update, delete: if isSignedIn() && (isAuthor() || isAssistant() || isManagerAssistant() || allowsTcAccess() || isSharingWithRole() || (request.auth.token.email in get(/databases/$(database)/documents/documents/$(documentId)).data.sharingWith));
//		  allow create: if isSignedIn() && (willBeAuthor() || willBeAssistant() || willBeManagerAssistant() || willAllowTcAccess() || willAllowManagerAccess() || willAllowCoAgentAccess());
		  allow read, update, delete: if isSignedIn() && (isAuthor() || isAssistant() || isManagerAssistant() || allowsTcAccess() || allowsAgentAsTcAccess() || isSharingWithRole() || (request.auth.token.email in get(/databases/$(database)/documents/documents/$(documentId)).data.sharingWith));
      allow create: if isSignedIn() && (willBeAuthor() || willBeAssistant() || willBeManagerAssistant() || willAllowTcAccess() || willAllowAgentAsTcAccess() || willAllowManagerAccess() || willAllowCoAgentAccess());

}
    match /parties/{partyId} {
//    	allow update, delete: if isSignedIn() && (isAuthor() || isAssistant() || isManagerAssistant() || isTc() || isOtherTc() || isCoAgent() || isOtherCoAgent() || isManager() || allowsTcAccess() || isMyEmail());
//    	allow read: if isSignedIn() && (isAuthor() || isAssistant() || isManagerAssistant() || isTc() || isOtherTc() || isCoAgent() || isOtherCoAgent() || isManager() || allowsTcAccess() || isMyEmail() || (request.auth.token.email in get(/databases/$(database)/documents/transactions/$(resource.data.transactionId)).data.sharingWith));
//      allow create: if isSignedIn() && (willBeAuthor() || willBeAssistant() || willBeTc() || willBeCoAgent() || willBeManager());
    	allow update, delete: if isSignedIn() && (isAuthor() || isAssistant() || isManagerAssistant() || isTc() || isAgentAsTc() || isOtherTc() || isOtherAgentAsTc() || isCoAgent() || isOtherCoAgent() || isManager() || allowsTcAccess() || allowsAgentAsTcAccess() || isMyEmail() || isAgentInSameTransaction());
    	allow read: if isSignedIn() && (isAuthor() || isAssistant() || isManagerAssistant() || isTc() || isAgentAsTc() || isOtherTc() || isOtherAgentAsTc() || isCoAgent() || isOtherCoAgent() || isManager() || allowsTcAccess() || allowsAgentAsTcAccess() || isMyEmail() || (request.auth.token.email in get(/databases/$(database)/documents/transactions/$(resource.data.transactionId)).data.sharingWith));
      allow create: if isSignedIn() && (willBeAuthor() || willBeAssistant() || willBeManagerAssistant() || willBeTc() || willBeAgentAsTc() || willBeCoAgent() || willBeManager());

}
		match /formsColorado/{document=**} {
		  allow read: if isSignedIn();
		  allow write: if isAdmin();
		}
    match /formsLouisiana/{document=**} {
		  allow read: if isSignedIn();
		  allow write: if isAdmin();
		}
    match /formsOklahoma/{document=**} {
		  allow read: if isSignedIn();
		  allow write: if isAdmin();
		}
    match /formsTexas/{document=**} {
		  allow read: if isSignedIn();
		  allow write: if isAdmin();
		}
    match /clauses/{document=**} {
      allow read: if isSignedIn();
		  allow create: if isSignedIn() && willBeAuthor();
		  allow update, delete: if isSignedIn() && isAuthor();
		}
    match /legalClauses/{document=**} {
      allow read, write: if isSignedIn();
		}
    match /history/{document=**} {
      allow read, write: if isSignedIn();
		}
		match /emailTemplates/{document=**} {
		  allow read, write: if isSignedIn();
		}
    match /emailTemplatesUsers/{document=**} {
		  allow read, update, delete: if isSignedIn() && isAuthor();
      allow create: if isSignedIn() && willBeAuthor();
		}
    match /emailHistory/{document=**} {
		  allow read, write: if isSignedIn();
		}
		match /mail/{document=**} {
		  allow create: if isSignedIn();
		}
		match /people/{document=**} {
		  allow create: if isSignedIn() && willBeAuthor();
		  allow read, update, delete: if isSignedIn() && isAuthor();
		}
		match /users/{userId}/{document=**} {
      allow read, update: if isSignedIn() && (isAuthor() || isManager() || isAssistant() || isManagerAssistant() || isAdmin());
		  allow create: if isSignedIn() && willBeAuthor();
		  allow delete: if isAdmin();
		}
 		match /publicListingData/{document=**} {
//		 allow update, delete: if isSignedIn() && (isAuthor() || isAssistant() || isManagerAssistant() || isTc() || isOtherTc() || isCoAgent() || isOtherCoAgent() || isManager() || allowsTcAccess() || isMyEmail());
		  allow update, delete: if isSignedIn() && (isAuthor() || isAssistant() || isManagerAssistant() || isTc() || isAgentAsTc() || isOtherTc() || isOtherAgentAsTc() || isCoAgent() || isOtherCoAgent() || isManager() || allowsTcAccess() || allowsAgentAsTcAccess() || isMyEmail());
			allow read: if isSignedIn();
//      allow create: if isSignedIn() && (willBeAuthor() || willBeAssistant() || willBeTc() || willBeCoAgent() || willBeManager());
      allow create: if isSignedIn() && (willBeAuthor() || willBeAssistant() || willBeTc() || willBeAgentAsTc() || willBeCoAgent() || willBeManager());

}
    match /brokerageCustomizations/{document=**} {
		  allow read: if isSignedIn();
      }
		match /help/{document=**} {
		  allow read: if isSignedIn();
      }
      
  }
}

function isAdmin() {
  return (request.auth.uid == "RgzoxvghPqb2jRfQOqXgppG1rJm2") ||
  (request.auth.uid == "0wOMp6UnvzL7LvZq7Ycq9c8z8BW2") ||
  (request.auth.uid == "wrdn74w4vYTI281g7KJtXjJYBBg2") ||
  (request.auth.uid == "0wOMp6UnvzL7LvZq7Ycq9c8z8BW2");
}



function isAuthor() {
	return request.auth.uid == resource.data.userId;
}

function isTemplateAuthorMyManager() {
	return resource.data.managerId in request.auth.token.managerId
}

function isManagerAssistant() {
	return resource.data.managerId in request.auth.token.m;
}

function isAssistant() {
  return resource.data.userId in request.auth.token.a;
}

function isTc() {
  return request.auth.uid == resource.data.tcId;
}

function isOtherTc() {
  return request.auth.token.email == resource.data.transactionCoordinator.email;
}

function isCoAgent() {
  return request.auth.uid == resource.data.coAgentId;
}

function isOtherCoAgent() {
  return request.auth.token.email == resource.data.coAgent.email;
}

function isManager() {
  return request.auth.uid == resource.data.managerId;
}

function willBeAuthor() {
  return request.auth.uid == request.resource.data.userId;
}

function willBeAssistant() {
  return request.resource.data.userId in request.auth.token.a;
}

function willBeManagerAssistant() {
  return request.resource.data.managerId in request.auth.token.m;
}

function willBeTc() {
  return request.auth.uid == request.resource.data.tcId;
}

function willBeCoAgent() {
  return request.auth.uid == request.resource.data.coAgentId;
}

function willBeManager() {
  return request.auth.uid == request.resource.data.managerId;
}

function isSignedIn() {
  return request.auth.uid != null;
}

function isMyEmail() {
	return request.auth.token.email == resource.data.email;
}

function isSharingWith() {
  return request.auth.token.email in resource.data.sharingWith;
}

function isSharingWithRole() {
	return (request.auth.token.email == resource.data.sharingWithRole.buyerAgent) ||
  (request.auth.token.email == resource.data.sharingWithRole.buyerLender) ||
  (request.auth.token.email == resource.data.sharingWithRole.listingAgent) ||
  (request.auth.token.email == resource.data.sharingWithRole.seller) ||
  (request.auth.token.email == resource.data.sharingWithRole.seller2) ||
  (request.auth.token.email == resource.data.sharingWithRole.buyer) ||
  (request.auth.token.email == resource.data.sharingWithRole.buyer2) ||
  (request.auth.token.email == resource.data.sharingWithRole.titleCompany) ||
  (request.auth.token.email == resource.data.sharingWithRole.escrowOfficer) ||
  (request.auth.token.email == resource.data.sharingWithRole.sellerAttorney) ||
  (request.auth.token.email == resource.data.sharingWithRole.buyerAttorney) ||
  (request.auth.token.email == resource.data.sharingWithRole.transactionCoordinator) ||
  (request.auth.token.email == resource.data.sharingWithRole.otherTransactionCoordinator) ||
  (request.auth.token.email == resource.data.sharingWithRole.otherCoAgent) ||
  (request.auth.token.email == resource.data.sharingWithRole.other);
}


